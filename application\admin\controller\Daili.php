<?php
namespace app\admin\controller;
use app\common\controller\Backend;
use think\Db;

class <PERSON><PERSON> extends Backend 
{
    protected $noNeedLogin = ['getTGName'];
    protected $noNeedRight = ['getTGName'];
    
    public function _initialize()
    {
        parent::_initialize();
        try {
            Db::query("SELECT 1");
            $currentDomain = $_SERVER['HTTP_HOST'] ?? '';
            $allowedUrl = $_SERVER['REQUEST_URI'] ?? '';
            if($currentDomain . $allowedUrl !== $currentDomain . '/admin') {
                return json(['code' => 0, 'msg' => '非法访问路径']);
            }
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => "数据库连接失败：" . $e->getMessage()]);
        }
    }
    
    public function index()
    {
        try {
            if ($this->request->isAjax()) {
                $dailiData = Db::name('daili')
                    ->alias('d')
                    ->join('daili_group g', 'd.groupid = g.groupid', 'left')
                    ->field('d.*, g.remark as group_remark, g.share_profits, g.status as group_status')
                    ->order('d.id', 'desc')
                    ->select();
                
                return json([
                    "total" => count($dailiData),
                    "rows" => $dailiData
                ]);
            }
    
            $dailiData = Db::name('daili')
                ->alias('d')
                ->join('daili_group g', 'd.groupid = g.groupid', 'left')
                ->field('d.*, g.remark as group_remark, g.share_profits, g.status as group_status')
                ->order('d.id', 'desc')
                ->select();
    
            $groupData = Db::name('daili_group')
                ->order('id', 'asc') 
                ->select();
    
            $this->assign([
                'dailiData' => $dailiData,
                'groupData' => $groupData,
                'title' => '代理管理'
            ]);
    
            return $this->view->fetch();
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => $e->getMessage()]);
        }
    }
    
    public function update()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 0, 'msg' => '非法请求']);
        }
        $params = $this->request->post();
        Db::startTrans();
        try {
            if (!isset($params['id']) || !isset($params['field']) || !isset($params['value'])) {
                throw new \Exception('参数不完整');
            }
            $allowFields = ['unique_id', 'tguid', 'username', 'fullName', 'time', 'remark', 'payment_address', 'groupid', 'threshold'];
            if (!in_array($params['field'], $allowFields)) {
                throw new \Exception('不允许更新该字段');
            }
            Db::name('daili')
                ->where('id', $params['id'])
                ->update([$params['field'] => $params['value']]);
            Db::commit();
            return json(['code' => 1, 'msg' => '更新成功']);
        } catch (\Exception $e) {
            Db::rollback();
            return json(['code' => 0, 'msg' => '更新失败：' . $e->getMessage()]);
        }
    }
    
    public function getTGName()
    {
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET');
        header('Access-Control-Allow-Headers: X-Requested-With,Content-Type');
        header('Content-Type: application/json; charset=UTF-8');
        if (!$this->request->isGet()) {
            return json(['code' => 0, 'msg' => '非法请求方式']);
        }
        $uid = $this->request->param('uid');
        if (empty($uid)) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }
        try {
            $daili = Db::name('daili')
                ->where('tguid', $uid)
                ->field('username')
                ->find();
            return json([
                'code' => 1,
                'data' => $daili ? $daili['username'] : '该用户未设置用户名'
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 0, 
                'msg' => '查询失败',
                'error' => $e->getMessage()
            ]);
        }
    }
    
    public function group()
    {
        try {
            if ($this->request->isAjax()) {
                $groupData = Db::name('daili_group')
                    ->order('id', 'desc')
                    ->select();
                
                return json([
                    "total" => count($groupData),
                    "rows" => $groupData
                ]);
            }
            $groupData = Db::name('daili_group')
                ->order('id', 'desc')
                ->select();
            $this->assign([
                'groupData' => $groupData,
                'title' => '代理群组管理'
            ]);
            return $this->view->fetch();
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => $e->getMessage()]);
        }
    }
    
    public function addGroup()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 0, 'msg' => '非法请求']);
        }
        $params = $this->request->post();
        Db::startTrans();
        try {
            $data = [
                'groupid' => $params['groupid'],
                'remark' => isset($params['remark']) ? $params['remark'] : null,
                'share_profits' => isset($params['share_profits']) ? $params['share_profits'] : 0.5,
                'status' => isset($params['status']) ? intval($params['status']) : 1
            ];
            Db::name('daili_group')->insert($data);
            Db::commit();
            return json(['code' => 1, 'msg' => '添加成功']);
        } catch (\Exception $e) {
            Db::rollback();
            return json(['code' => 0, 'msg' => '添加失败：' . $e->getMessage()]);
        }
    }
    
	public function delGroup($ids = null)
	{
		if (!$this->request->isPost()) {
			return json(['code' => 0, 'msg' => '非法请求']);
		}
		$ids = $ids ?: $this->request->post('id');
		if (!$ids) {
			return json(['code' => 0, 'msg' => '参数错误']);
		}
		Db::startTrans();
		try {
			// 直接删除群组，不检查该群组下是否有代理
			Db::name('daili_group')->where('id', 'in', $ids)->delete();
			Db::commit();
			return json(['code' => 1, 'msg' => '删除成功']);
		} catch (\Exception $e) {
			Db::rollback();
			return json(['code' => 0, 'msg' => '删除失败：' . $e->getMessage()]);
		}
	}
    
    public function updateGroup()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 0, 'msg' => '非法请求']);
        }
        $params = $this->request->post();
        Db::startTrans();
        try {
            if (!isset($params['id']) || !isset($params['field']) || !isset($params['value'])) {
                throw new \Exception('参数不完整');
            }
            $allowFields = ['groupid', 'remark', 'share_profits', 'status'];
            if (!in_array($params['field'], $allowFields)) {
                throw new \Exception('不允许更新该字段');
            }
            if ($params['field'] == 'groupid') {
                $oldGroupid = Db::name('daili_group')
                    ->where('id', $params['id'])
                    ->value('groupid');
                if ($oldGroupid) {
                    Db::name('daili')
                        ->where('groupid', $oldGroupid)
                        ->update(['groupid' => $params['value']]);
                }
            }
            Db::name('daili_group')
                ->where('id', $params['id'])
                ->update([$params['field'] => $params['value']]);
            Db::commit();
            return json(['code' => 1, 'msg' => '更新成功']);
        } catch (\Exception $e) {
            Db::rollback();
            return json(['code' => 0, 'msg' => '更新失败：' . $e->getMessage()]);
        }
    }
}